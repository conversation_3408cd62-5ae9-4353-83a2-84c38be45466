from http import HTTPStatus
from typing import TYPE_CHECKING, cast

from aws_lambda_powertools.event_handler import Response
from aws_lambda_powertools.event_handler.router import APIGatewayRouter

from eyecue_things_api.context import Context
from eyecue_things_api.response import SuccessResponse
from eyecue_things_api.services.tracker.use_cases import UseCasesService

if TYPE_CHECKING:
    from aws_lambda_powertools.event_handler.openapi.types import OpenAPIResponse
from typing import TYPE_CHECKING, Annotated

from aws_lambda_powertools.event_handler.openapi.params import Body, Path

from eyecue_things_api.models.use_cases.use_cases import UseCase

router = APIGatewayRouter()
use_cases_service = UseCasesService(Context())

responses = cast(
    "dict[int, OpenAPIResponse]",
    {
        HTTPStatus.OK: {"description": "Success"},
        HTTPStatus.NOT_FOUND: {"description": "Not Found"},
        HTTPStatus.FORBIDDEN: {"description": "Forbidden"},
        HTTPStatus.INTERNAL_SERVER_ERROR: {"description": "Server Error"},
    },
)


@router.get(
    rule="/use-cases",
    tags=["Use Cases"],
    summary="Get list of all available use cases",
    operation_id="getUseCases",
    responses=responses,
)
def get_use_cases() -> Response[list]:
    use_cases = use_cases_service.get_all_use_cases()
    return SuccessResponse[list](use_cases)


@router.post(
    rule="/use-cases",
    tags=["Use Cases"],
    summary="Create a new use case",
    operation_id="createUseCase",
    responses=responses,
)
def create_use_case(
    use_case: Annotated[UseCase, Body(description="Use Case payload.")],
) -> Response[UseCase]:
    use_cases = use_cases_service.create(use_case)
    return SuccessResponse[UseCase](use_cases)


@router.put(
    rule="/use-cases/<use_case_id>",
    tags=["Use Cases"],
    summary="Update an existing use case",
    operation_id="updateUseCase",
    responses=responses,
)
def update_use_case(
    use_case_id: Annotated[str, Path(description="The use case id.")],
    use_case: Annotated[UseCase, Body(description="Use Case payload.")],
) -> Response[UseCase]:
    use_cases = use_cases_service.update(use_case_id, use_case)
    return SuccessResponse[UseCase](use_cases)


@router.delete(
    rule="/use-cases/<use_case_id>",
    tags=["Use Cases"],
    summary="Delete an existing use case",
    operation_id="deleteUseCase",
    responses=responses,
)
def delete_use_case(
    use_case_id: Annotated[str, Path(description="The use case id.")],
) -> Response[None]:
    use_cases = use_cases_service.delete(use_case_id)
    return SuccessResponse[None](use_cases)
