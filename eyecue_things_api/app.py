from aws_lambda_powertools import Logger
from aws_lambda_powertools.event_handler import APIGatewayRestResolver
from aws_lambda_powertools.logging import correlation_paths
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.exceptions.exception_handler import register_exception_handlers
from eyecue_things_api.routes.router import router
from eyecue_things_api.util import add_cors_headers

logger = Logger()

app = APIGatewayRestResolver(enable_validation=True, strip_prefixes=["/api/v1"])
register_exception_handlers(app)
app.include_router(router)


@logger.inject_lambda_context(correlation_id_path=correlation_paths.API_GATEWAY_REST)
def lambda_handler(
    event: dict, context: LambdaContext, app_context: Context | None = None
) -> dict:
    """Handle the routing of events from the API Gateway to the functions.

    Args:
        event (dict): Event data passed to the Lambda function
        context (LambdaContext): Lambda context object
        app_context (Context | None): Application context (only used for testing).

    Returns:
        dict: Response data to be returned to the API Gateway.

    """
    if app_context is None:
        app_context = Context()

    app.append_context(
        lambda_context=context,
        app_context=app_context,
    )
    response = app.resolve(event, context)

    return add_cors_headers(response)
