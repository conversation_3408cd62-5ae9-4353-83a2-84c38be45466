from http import HTTPStatus
from typing import TYPE_CHECKING, Annotated, cast

from aws_lambda_powertools.event_handler import Response
from aws_lambda_powertools.event_handler.openapi.params import Body, Path
from aws_lambda_powertools.event_handler.router import APIGatewayRouter

from eyecue_things_api.context import Context
from eyecue_things_api.models.discriminator import Camera<PERSON><PERSON>
from eyecue_things_api.response import SuccessResponse
from eyecue_things_api.services.tracker.cameras import CamerasService

if TYPE_CHECKING:
    from aws_lambda_powertools.event_handler.openapi.types import OpenAPIResponse

router = APIGatewayRouter()
cameras_service = CamerasService(Context())

responses = cast(
    "dict[int, OpenAPIResponse]",
    {
        HTTPStatus.OK: {"description": "Success"},
        HTTPStatus.NOT_FOUND: {"description": "Not Found"},
        HTTPStatus.FORBIDDEN: {"description": "Forbidden"},
        HTTPStatus.INTERNAL_SERVER_ERROR: {"description": "Server Error"},
    },
)


@router.get(
    rule="/sites/<site_id>/cameras/<camera_id>",
    tags=["Cameras"],
    summary="Get camera by site and camera ID",
    operation_id="getCamera",
    responses=responses,
)
def get_camera(
    site_id: Annotated[str, Path(description="The site id of the store")],
    camera_id: Annotated[
        str, Path(description="The camera id as numbers (example: 019)")
    ],
) -> Response[CameraTracker]:
    camera_tracker = cameras_service.get(site_id, camera_id)
    return SuccessResponse[CameraTracker](camera_tracker)


@router.post(
    rule="/sites/<site_id>/cameras",
    tags=["Cameras"],
    summary="Create camera with site and camera ID",
    operation_id="createCamera",
    responses=responses,
)
def create_camera(
    site_id: Annotated[str, Path(description="The site id of the store")],
    tracker: Annotated[
        CameraTracker,
        Body(discriminator="service", description="Indoor or Drive-Thru tracker"),
    ],
) -> Response[CameraTracker]:
    camera_tracker = cameras_service.create(site_id, tracker)
    return SuccessResponse[CameraTracker](camera_tracker)


@router.put(
    rule="/sites/<site_id>/cameras/<camera_id>",
    tags=["Cameras"],
    summary="Update camera with site and camera ID",
    operation_id="updateCamera",
    responses=responses,
)
def update_indoor_tracker(
    site_id: Annotated[str, Path(description="The site id of the store")],
    camera_id: Annotated[
        str, Path(description="The camera id as numbers (example: 019)")
    ],
    tracker: Annotated[
        CameraTracker,
        Body(discriminator="service", description="Indoor or Drive-Thru tracker"),
    ],
) -> Response[CameraTracker]:
    camera_tracker = cameras_service.update(site_id, camera_id, tracker)
    return SuccessResponse[CameraTracker](camera_tracker)


@router.delete(
    rule="/sites/<site_id>/cameras/<camera_id>",
    tags=["Cameras"],
    summary="Delete camera tracker",
    operation_id="deleteCamera",
    responses=responses,
)
def delete_camera(
    site_id: Annotated[str, Path(description="The site id of the store")],
    camera_id: Annotated[
        str, Path(description="The camera id as numbers (example: 019)")
    ],
) -> Response[None]:
    cameras_service.delete(site_id, camera_id)
    return SuccessResponse[None](None)
