# ruff: noqa: PLC0206
# ruff: noqa: D205
# ruff: noqa: D401
# mypy: ignore-errors
import uuid as uuid_lib
from copy import deepcopy
from datetime import UTC, datetime
from decimal import Decimal
from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from botocore.exceptions import ClientError
from mypy_boto3_dynamodb.service_resource import Table
from pydantic import ValidationError

from eyecue_things_api.models.discriminator import CameraTracker, TrackerRoi
from eyecue_things_api.models.exceptions import (
    ItemDeleteError,
    ItemExistsError,
    ItemNotFoundError,
    ModelTypeError,
    ROINotFoundError,
)
from eyecue_things_api.response import (
    ErrorBody,
    ErrorResponse,
    ValidationErrorBody,
    ValidationErrorResponse,
)

logger = Logger()


def parse_uuid(uuid_str: str) -> uuid_lib.UUID | None:
    try:
        return uuid_lib.UUID(uuid_str)
    except ValueError as e:
        raise ValueError(f"The provided UUID is invalid: {uuid_str}") from e


def format_validation_error(e: ValidationError) -> str:
    """Format a Pydantic ValidationError into a dictionary.

    Args:
        e (ValidationError): The Pydantic ValidationError to format

    Returns:
        dict[str, Any]: The formatted error

    """
    formatted_errors = [
        f"Field '{'.'.join(str(loc) for loc in error['loc'])}': {error['msg']}"
        for error in e.errors()
    ]

    return (
        f"Validation failed with {len(formatted_errors)} error(s):\n"
        f"{'\n'.join(formatted_errors)}"
    )


def exception_handler(exception: Exception) -> ErrorResponse:  # noqa: PLR0911
    """Generate an ErrorResponse from an exception.

    See: https://blog.ploeh.dk/2014/12/23/exception-messages-are-for-programmers/
    """
    logger.exception(exception)

    if isinstance(exception, ItemExistsError):
        return ErrorResponse(
            status_code=HTTPStatus.BAD_REQUEST,
            body=ErrorBody(
                message=exception.message,
                error=HTTPStatus.BAD_REQUEST.phrase,
            ),
        )

    if isinstance(exception, ItemNotFoundError):
        return ErrorResponse(
            status_code=HTTPStatus.NOT_FOUND,
            body=ErrorBody(
                error=HTTPStatus.NOT_FOUND.phrase,
                message=exception.message,
            ),
        )

    if isinstance(exception, ItemDeleteError):
        return ErrorResponse(
            status_code=HTTPStatus.NOT_FOUND,
            body=ErrorBody(
                error=HTTPStatus.NOT_FOUND.phrase,
                message=exception.message,
            ),
        )

    if isinstance(exception, ModelTypeError):
        return ErrorResponse(
            status_code=HTTPStatus.BAD_REQUEST,
            body=ErrorBody(
                error=HTTPStatus.BAD_REQUEST.phrase,
                message=exception.message,
            ),
        )

    if isinstance(exception, ValidationError):
        message = format_validation_error(exception)
        return ValidationErrorResponse(
            status_code=HTTPStatus.BAD_REQUEST.value,
            body=ValidationErrorBody(
                error=HTTPStatus.BAD_REQUEST.phrase,
                message=message,
                details=exception.errors(),
            ),
        )

    if isinstance(exception, ClientError):
        return ErrorResponse(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            body=ErrorBody(
                message="An error occurred during an AWS operation",
                error=HTTPStatus.INTERNAL_SERVER_ERROR.phrase,
            ),
        )

    return ErrorResponse(
        status_code=500,
        body=ErrorBody(
            message="An unexpected error occurred",
            error=HTTPStatus.INTERNAL_SERVER_ERROR.phrase,
        ),
    )


def convert_floats_to_decimal(data: dict) -> dict:  # type: ignore[type-arg]
    """Recursively convert all float values in a dictionary (or list) to Decimal.

    Args:
        data (dict | list | any): Input dictionary or list to process.

    Returns:
        dict | list | any: Processed data with floats replaced by Decimals.

    """
    if isinstance(data, dict):
        return {k: convert_floats_to_decimal(v) for k, v in data.items()}
    if isinstance(data, list):
        return [convert_floats_to_decimal(v) for v in data]
    if isinstance(data, float):
        return Decimal(str(data))  # Convert float to Decimal
    return data  # Return other data types unchanged


def recursive_merge_with_null_deletions(old: dict, new: dict) -> dict:  # type: ignore[type-arg]
    """Merge two dictionaries according to DynamoDB update rules:
            - If a key is removed from a dictionary, set its value to
              None instead of deleting it.
            - If a value is changed in the new dictionary, retain the new value.
            - If an item is removed from a list, delete it from the list.

    Args:
        old (dict): The JSON representing the current state in DynamoDB.
        new (dict): The JSON representing the user's intended updates.

    Returns:
        dict: A merged JSON dictionary updated according to the rules above.

    """
    result = deepcopy(new)

    for key in old:
        if key not in result:
            result[key] = None
        else:
            old_val = old[key]
            new_val = result[key]
            if isinstance(old_val, dict) and isinstance(new_val, dict):
                result[key] = recursive_merge_with_null_deletions(old_val, new_val)
            elif isinstance(old_val, list) and isinstance(new_val, list):
                result[key] = merge_lists_recursively(old_val, new_val)
    return result


def merge_lists_recursively(old_list: list, new_list: list) -> list:  # type: ignore[type-arg]
    """Helper function used by `recursive_merge_with_null_deletions` to
       apply merging rules for lists.

    Args:
        old_list (list): The list representing the current state in DynamoDB.
        new_list (list): The list representing the user's intended updates.

    Returns:
        list: A merged list updated according to the defined rules.

    """
    merged = []

    for i, new_item in enumerate(new_list):
        if i < len(old_list):
            old_item = old_list[i]
            if isinstance(old_item, dict) and isinstance(new_item, dict):
                merged.append(recursive_merge_with_null_deletions(old_item, new_item))
            else:
                merged.append(new_item)
        else:
            merged.append(new_item)  # extra items in new_list
    return merged


def find_roi(
    tracker: CameraTracker,
    uuid: uuid_lib.UUID | str,
    silent: bool = False,  # noqa: FBT001, FBT002
) -> tuple[int, TrackerRoi] | None:
    """Search for an ROI based on a given UUID.

    Args:
        tracker (CameraTracker): A Tracker or IndoorTracker object to search
            for the ROI.
        uuid (uuid_lib.UUID): The UUID of the ROI to search for.
        silent (bool): Whether to thrown an error if the RoI was not found.

    Returns:
        tuple[int, TrackerRoi]: A tuple containing the index at which the
            ROI or IndoorRoi is found in the list, and the corresponding ROI or
            IndoorRoi object.

    """
    if not tracker.rois:
        raise ROINotFoundError(tracker.site_id, tracker.camera_id, uuid)
    if isinstance(uuid, str):
        uuid = parse_uuid(uuid)
        if uuid is None:
            raise ValueError("The provided UUID is invalid.")
    for index, roi in enumerate(tracker.rois):
        if roi.uuid == uuid:
            return index, roi
    if silent:
        return None
    raise ROINotFoundError(tracker.site_id, tracker.camera_id, uuid)


def add_cors_headers(response: dict) -> dict:
    """Search for an ROI based on a given UUID.

    Args:
        response (dict): A dict that contains an API Response to a request.

    Returns:
        dict: A dict that contains an API Response to a request including CORS headers.

    """
    response["headers"] = {
        **response.get("headers", {}),
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",  # noqa: E501
        "Access-Control-Allow-Methods": "GET,POST,OPTIONS",
    }
    return response


def get_thing_record_name(site_id: str, camera_id: str) -> str:
    """Generate the thing name for the eyecue_things table based on
    the site_id and camera_id.

    Args:
        site_id (str): site id of the store.
        camera_id (str): camera id (only numbers and leading zeros)

    Returns:
        str: full name of the DynamoDB record.

    """
    return f"eyeq-tracker-{site_id}-camera{camera_id}"


def timestamp_now_factory() -> str:
    return datetime.now(UTC).isoformat()


def get_keys_from_table(
    table: Table, *, include_keys_types: bool = False
) -> dict[str, str]:
    """Given a boto3 DynamoDB Table resource object, \
    return a list of the key attribute names (PK and SK if any).
    """
    table.load()
    table_keys = {key["KeyType"]: key["AttributeName"] for key in table.key_schema}
    if include_keys_types:
        return table_keys
    return list(table_keys.values())
