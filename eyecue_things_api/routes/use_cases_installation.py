from http import HTTPStatus
from typing import TYPE_CHECKING, cast

from aws_lambda_powertools.event_handler import Response
from aws_lambda_powertools.event_handler.router import APIGatewayRouter

from eyecue_things_api.context import Context
from eyecue_things_api.response import SuccessResponse
from eyecue_things_api.services.tracker.use_cases_installation import (
    UseCasesInstallationService,
)

if TYPE_CHECKING:
    from aws_lambda_powertools.event_handler.openapi.types import OpenAPIResponse
from typing import TYPE_CHECKING, Annotated

from aws_lambda_powertools.event_handler.openapi.params import Body, Path

from eyecue_things_api.models.types import SiteId
from eyecue_things_api.models.use_cases.types import UseCaseInstallationId
from eyecue_things_api.models.use_cases.use_cases_installations import (
    UseCaseInstallation,
)

router = APIGatewayRouter()
use_cases_installation_service = UseCasesInstallationService(Context())

responses = cast(
    "dict[int, OpenAPIResponse]",
    {
        HTTPStatus.OK: {"description": "Success"},
        HTTPStatus.NOT_FOUND: {"description": "Not Found"},
        HTTPStatus.FORBIDDEN: {"description": "Forbidden"},
        HTTPStatus.INTERNAL_SERVER_ERROR: {"description": "Server Error"},
    },
)


@router.get(
    rule="/installations",
    tags=["Use Cases Installations"],
    summary="Get list of all available use cases",
    operation_id="getUseCaseInstallations",
    responses=responses,
)
def get_all_use_case_installations() -> Response[list[UseCaseInstallation]]:
    use_case_installations = (
        use_cases_installation_service.get_all_use_cases_installations()
    )
    return SuccessResponse[list[UseCaseInstallation]](use_case_installations)


@router.get(
    rule="/installations/sites/<site_id>",
    tags=["Use Cases Installations"],
    summary="Get list of all available use cases",
    operation_id="getUseCaseInstallations",
    responses=responses,
)
def get_all_use_case_installations_filtered(
    site_id: Annotated[SiteId, Path(description="The site id of the store.")],
) -> Response[list[UseCaseInstallation]]:
    use_case_id = router.current_event.get_query_string_value(name="use_case_id")
    use_case_installations = (
        use_cases_installation_service.get_all_use_case_installations_filtered(
            site_id=site_id, use_case_id=use_case_id
        )
    )
    return SuccessResponse[list[UseCaseInstallation]](use_case_installations)


@router.post(
    rule="/installations",
    tags=["Use Cases Installations"],
    summary="Create a new use case installation",
    operation_id="createUseCaseInstallation",
    responses=responses,
)
def create_use_case_installation(
    use_case: Annotated[
        UseCaseInstallation, Body(description="Use case installation payload.")
    ],
) -> Response[UseCaseInstallation]:
    use_case_installations = use_cases_installation_service.create(use_case)
    return SuccessResponse[UseCaseInstallation](use_case_installations)


@router.put(
    rule="/installations/<installation_id>",
    tags=["Use Cases Installations"],
    summary="Update an existing use case installation",
    operation_id="updateUseCase",
    responses=responses,
)
def update_use_case_installation(
    installation_id: Annotated[
        UseCaseInstallationId, Path(description="The use case installation id.")
    ],
    use_case: Annotated[
        UseCaseInstallation, Body(description="Use case installation payload.")
    ],
) -> Response[UseCaseInstallation]:
    updated_use_case_installation = use_cases_installation_service.update(
        installation_id, use_case
    )
    return SuccessResponse[UseCaseInstallation](updated_use_case_installation)


@router.delete(
    rule="/installations/<installation_id>",
    tags=["Use Cases Installations"],
    summary="Delete an existing use case installation",
    operation_id="deleteUseCaseInstallation",
    responses=responses,
)
def delete_use_case_installation(
    installation_id: Annotated[
        UseCaseInstallationId, Path(description="The use case installation id.")
    ],
) -> Response[None]:
    use_cases_installation_service.delete(installation_id)
    return SuccessResponse[None](None)
